---
description: Repository Information Overview
alwaysApply: true
---

# SuperCare App Information

## Summary
SuperCare App is a comprehensive healthcare management system built with .NET 8. It follows Clean Architecture principles and provides features for user management, appointment scheduling, care provider management, and medical record handling. The application uses PostgreSQL for data storage and includes Docker support for easy deployment.

## Structure
The repository follows a Clean Architecture pattern with clear separation of concerns:
- **SuperCareApp.Domain**: Contains domain entities, enums, exceptions, and interfaces
- **SuperCareApp.Application**: Contains business logic and application interfaces
- **SuperCareApp.Persistence**: Implements data access and storage
- **super-care-app**: API Controllers, Filters, Middleware (main project)
- **SuperCareApp.Persistence.Test**: Test project for persistence layer

## Language & Runtime
**Language**: C#
**Version**: .NET 8.0
**Build System**: MSBuild
**Package Manager**: NuGet
**Database**: PostgreSQL 16

## Dependencies
**Main Dependencies**:
- Entity Framework Core 8.0.14
- ASP.NET Core Identity
- JWT Authentication
- Swashbuckle/Swagger for API documentation
- FluentValidation 11.3.0
- MailKit/MimeKit 4.11.0
- Twilio.AspNet.Core 8.1.1
- Npgsql.EntityFrameworkCore.PostgreSQL 8.0.11

**Development Dependencies**:
- xUnit 2.5.3
- Moq 4.20.72
- Microsoft.EntityFrameworkCore.InMemory 8.0.14
- Microsoft.NET.Test.Sdk 17.8.0

## Build & Installation
```bash
# Restore dependencies
dotnet restore super-care-app.sln

# Build the solution
dotnet build super-care-app.sln

# Run the application
dotnet run --project super-care-app/SuperCareApp.csproj

# Run tests
dotnet test SuperCareApp.Persistence.Test/SuperCareApp.Persistence.Test.csproj
```

## Docker
**Dockerfile**: Dockerfile
**Image**: Based on mcr.microsoft.com/dotnet/aspnet:8.0 and mcr.microsoft.com/dotnet/sdk:8.0
**Configuration**: Multi-stage build process with separate build and runtime stages
**Docker Compose**: Includes API service and PostgreSQL database

**Run with Docker**:
```bash
# Start all services
docker-compose up -d

# Start only the database
docker-compose up -d db
```

## Testing
**Framework**: xUnit
**Test Location**: SuperCareApp.Persistence.Test
**Naming Convention**: *Tests.cs suffix (e.g., CareCategoryServiceTests.cs)
**Configuration**: In-memory database for integration tests
**Run Command**:
```bash
dotnet test SuperCareApp.Persistence.Test/SuperCareApp.Persistence.Test.csproj
```

## Key Features
- **User Management**: Role-based access control with support for patients, doctors, and administrators
- **Authentication**: JWT-based authentication
- **Booking System**: Appointment scheduling and management
- **Care Provider Management**: Provider profiles, availability, and categories
- **Payment Processing**: Handling payments and invoices
- **Document Management**: Storage and verification of documents
- **Communication**: Messaging and notification system